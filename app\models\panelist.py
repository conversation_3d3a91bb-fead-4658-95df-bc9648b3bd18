"""
Panelist model for Interview Scheduling Microservice.

Represents panelist details and their availability slots with
proper validation and type hints.
"""

from datetime import date, time
from typing import List, Optional

from pydantic import BaseModel, Field, validator

from .base import BaseMongoModel, BaseCreateModel, BaseUpdateModel, PyObjectId


class Slot(BaseModel):
    """
    Represents an available time slot for a panelist.
    
    Contains all information about a specific time slot including
    booking status and references to booked interviews.
    """
    
    slot_id: str = Field(..., description="Unique ID for this specific slot")
    date: date = Field(..., description="Date of the slot (YYYY-MM-DD)")
    start_time: str = Field(..., description="Start time (HH:MM AM/PM)")
    end_time: str = Field(..., description="End time (HH:MM AM/PM)")
    timezone: str = Field(default="Asia/Kolkata", description="Timezone for the slot")
    is_booked: bool = Field(default=False, description="Whether the slot is booked")
    booked_candidate_id: Optional[str] = Field(
        default=None,
        description="References CandidateStatus.candidate_id if booked"
    )
    booked_interview_id: Optional[PyObjectId] = Field(
        default=None,
        description="References Interview._id if booked"
    )
    
    @validator("start_time", "end_time")
    def validate_time_format(cls, v: str) -> str:
        """
        Validate time format (HH:MM AM/PM).
        
        Args:
            v: Time string
            
        Returns:
            str: Validated time string
            
        Raises:
            ValueError: If time format is invalid
        """
        try:
            # Try to parse the time to validate format
            time.strptime(v, "%I:%M %p")
            return v
        except ValueError:
            raise ValueError("Time must be in HH:MM AM/PM format (e.g., '10:00 AM')")
    
    @validator("timezone")
    def validate_timezone(cls, v: str) -> str:
        """
        Validate timezone format.
        
        Args:
            v: Timezone string
            
        Returns:
            str: Validated timezone string
        """
        # Basic validation - could be enhanced with pytz
        if "/" not in v:
            raise ValueError("Timezone must be in format 'Continent/City' (e.g., 'Asia/Kolkata')")
        return v


class Panelist(BaseMongoModel):
    """
    Panelist model representing an interviewer and their availability.
    
    Stores panelist details and their available time slots for
    conducting interviews.
    """
    
    panelist_id: str = Field(..., description="Unique identifier for the panelist")
    name: str = Field(..., description="Panelist's full name")
    email: str = Field(..., description="Panelist's email address")
    available_slots: List[Slot] = Field(
        default_factory=list,
        description="Array of available time slots"
    )
    
    @validator("email")
    def validate_email(cls, v: str) -> str:
        """
        Basic email validation.
        
        Args:
            v: Email string
            
        Returns:
            str: Validated email
            
        Raises:
            ValueError: If email format is invalid
        """
        if "@" not in v or "." not in v.split("@")[-1]:
            raise ValueError("Invalid email format")
        return v.lower()
    
    @validator("available_slots")
    def validate_unique_slot_ids(cls, v: List[Slot]) -> List[Slot]:
        """
        Ensure all slot IDs are unique within a panelist.
        
        Args:
            v: List of slots
            
        Returns:
            List[Slot]: Validated list of slots
            
        Raises:
            ValueError: If duplicate slot IDs are found
        """
        slot_ids = [slot.slot_id for slot in v]
        if len(slot_ids) != len(set(slot_ids)):
            raise ValueError("All slot IDs must be unique within a panelist")
        return v
    
    def get_available_slots(self) -> List[Slot]:
        """
        Get all unbooked slots for this panelist.
        
        Returns:
            List[Slot]: List of available (unbooked) slots
        """
        return [slot for slot in self.available_slots if not slot.is_booked]
    
    def get_slot_by_id(self, slot_id: str) -> Optional[Slot]:
        """
        Get a specific slot by its ID.
        
        Args:
            slot_id: The slot ID to search for
            
        Returns:
            Optional[Slot]: The slot if found, None otherwise
        """
        for slot in self.available_slots:
            if slot.slot_id == slot_id:
                return slot
        return None
    
    def book_slot(self, slot_id: str, candidate_id: str, interview_id: PyObjectId) -> bool:
        """
        Book a specific slot for a candidate.
        
        Args:
            slot_id: The slot ID to book
            candidate_id: The candidate ID
            interview_id: The interview ID
            
        Returns:
            bool: True if slot was successfully booked, False if not found or already booked
        """
        slot = self.get_slot_by_id(slot_id)
        if slot and not slot.is_booked:
            slot.is_booked = True
            slot.booked_candidate_id = candidate_id
            slot.booked_interview_id = interview_id
            return True
        return False
    
    class Config:
        """Pydantic configuration."""
        collection_name = "panelist"


class PanelistCreate(BaseCreateModel):
    """Model for creating a new Panelist record."""
    
    panelist_id: str = Field(..., description="Unique identifier for the panelist")
    name: str = Field(..., description="Panelist's full name")
    email: str = Field(..., description="Panelist's email address")
    available_slots: List[Slot] = Field(
        default_factory=list,
        description="Array of available time slots"
    )
    
    @validator("email")
    def validate_email(cls, v: str) -> str:
        """Basic email validation."""
        if "@" not in v or "." not in v.split("@")[-1]:
            raise ValueError("Invalid email format")
        return v.lower()
    
    @validator("available_slots")
    def validate_unique_slot_ids(cls, v: List[Slot]) -> List[Slot]:
        """Ensure all slot IDs are unique within a panelist."""
        slot_ids = [slot.slot_id for slot in v]
        if len(slot_ids) != len(set(slot_ids)):
            raise ValueError("All slot IDs must be unique within a panelist")
        return v


class PanelistUpdate(BaseUpdateModel):
    """Model for updating a Panelist record."""
    
    name: Optional[str] = None
    email: Optional[str] = None
    available_slots: Optional[List[Slot]] = None
    
    @validator("email")
    def validate_email(cls, v: Optional[str]) -> Optional[str]:
        """Basic email validation."""
        if v is not None:
            if "@" not in v or "." not in v.split("@")[-1]:
                raise ValueError("Invalid email format")
            return v.lower()
        return v
