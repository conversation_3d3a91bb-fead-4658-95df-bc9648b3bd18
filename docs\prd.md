# Product Requirements Document (PRD) for Interview Scheduling Microservice (MVP)

## 1. Introduction

This document outlines the requirements for the Interview Scheduling Microservice (ISM), a standalone Python/FastAPI application. The primary goal of this MVP is to automate the process of scheduling interviews between eligible candidates and available panelists using automated phone calls via Plivo, managing all related data within its own MongoDB instance. This service will initially operate independently, with future plans for integration with the main Veda platform.

## 2. Goals

- Automate interview scheduling for candidates.
- Reduce manual effort for recruiters in coordinating interview times.
- Provide a self-service option for candidates to select interview slots via an interactive voice response (IVR) system.
- Track the status of interview scheduling attempts and scheduled interviews.

## 3. Out of Scope (for MVP)

- Direct integration with the Veda platform (no API calls from Veda to ISM, no webhooks from ISM to Veda).
- Skill-based matching for panelists (any panelist can interview any candidate for now).
- Complex retry logic for calls beyond a fixed number of attempts and delay.
- Candidate-initiated rescheduling of *booked* interviews.
- Panelist-initiated changes to *booked* interviews (e.g., reassigning due to unavailability).
- Google Meet link generation or email reminders to candidates/panelists (handled externally).
- Advanced analytics or reporting dashboards.
- Multi-tenancy.

## 4. Key Concepts & Definitions

- **Candidate:** An individual eligible for an interview, identified by `candidate_id`, `name`, `phone_number`, `email`.
- **Panelist:** An individual who conducts interviews, identified by `panelist_id`, `name`, `email`, and `available_slots`.
- **Available Slot:** A specific time window when a panelist is available for an interview.
- **Interview Round:** Defines the type of interview (e.g., "technical", "HR").
- **Plivo Agent Flow API:** Plivo's service for building and executing predefined call workflows, triggered by an API call.
- **Celery:** Python-based asynchronous task queue for managing call attempts and retries.
- **Redis:** Message broker for Celery.
- **MongoDB:** Primary data store for the microservice.

## 5. Data Models (MongoDB Schema)

### 5.1. `CandidateStatus` Collection

Stores the current state of a candidate within the interview scheduling process.

- `_id`: ObjectId (Primary Key)
- `candidate_id`: String (Unique identifier for the candidate)
- `name`: String
- `phone_number`: String (Required, E.164 format, e.g., "+919876543210")
- `email`: String
- `job_id`: String
- `round_info`: String (e.g., "technical", "HR")
- `company_name`: String
- `status`: Enum String (`PENDING_CALL`, `CALL_INITIATED`, `INTERVIEW_SCHEDULED`, `RETRY_REQUIRED`, `NOT_INTERESTED`, `MAX_RETRIES_EXCEEDED`)
- `interview_attempts`: Integer (Default: 0, increments on each call attempt)
- `last_attempted_at`: DateTime (Timestamp of the last call attempt)
- `scheduled_interview_id`: ObjectId (References `Interview` record, if scheduled)
- `created_at`: DateTime
- `updated_at`: DateTime

### 5.2. `Panelist` Collection

Stores panelist details and their availability.

- `_id`: ObjectId (Primary Key)
- `panelist_id`: String (Unique identifier for the panelist)
- `name`: String
- `email`: String
- `available_slots`: Array of `Slot` objects
  - `slot_id`: String (Unique ID for this specific slot)
  - `date`: Date (YYYY-MM-DD)
  - `start_time`: Time (HH:MM AM/PM)
  - `end_time`: Time (HH:MM AM/PM)
  - `timezone`: String (e.g., "Asia/Kolkata")
  - `is_booked`: Boolean (Default: `false`)
  - `booked_candidate_id`: String (References `CandidateStatus.candidate_id` if booked)
  - `booked_interview_id`: ObjectId (References `Interview._id` if booked)
- `created_at`: DateTime
- `updated_at`: DateTime

### 5.3. `Interview` Collection

Stores details of a successfully scheduled interview.

- `_id`: ObjectId (Primary Key)
- `candidate_id`: String (References `CandidateStatus.candidate_id`)
- `panelist_ids`: Array of Strings (References `Panelist.panelist_id` for all panelists involved)
- `job_id`: String
- `round_info`: String
- `company_name`: String
- `scheduled_time`: Object
  - `date`: Date (YYYY-MM-DD)
  - `start_time`: Time (HH:MM AM/PM)
  - `end_time`: Time (HH:MM AM/PM)
  - `timezone`: String
- `status`: Enum String (`SCHEDULED`, `CANCELLED`, `COMPLETED`, `NO_SHOW`)
- `created_at`: DateTime
- `updated_at`: DateTime

## 6. Functional Requirements

### 6.1. System Initialization / Seed Data

The microservice will accept an initial payload containing a list of `Candidate` objects, along with global `round_info`, `company_name`, `job_id`, and a list of `Panelist` objects with their `available_slots`.

This data will be used to populate the `CandidateStatus` and `Panelist` collections in the local MongoDB.

**Initial Payload Structure (for `POST /api/v1/start-scheduling`):**

```json
{
  "candidates": [
    {
      "candidate_id": "cand_001",
      "name": "Arjun Sharma",
      "phone_number": "+919876543210",
      "email": "<EMAIL>"
    },
    {
      "candidate_id": "cand_002",
      "name": "Priya Singh",
      "phone_number": "+918765432109",
      "email": "<EMAIL>"
    },
    {
      "candidate_id": "cand_003",
      "name": "Rahul Kumar",
      "phone_number": "+917654321098",
      "email": "<EMAIL>"
    }
  ],
  "round_info": "technical",
  "company_name": "Innovate Solutions Pvt. Ltd.",
  "job_id": "job_SWE_001",
  "panelists": [
    {
      "panelist_id": "panel_T1",
      "name": "Dr. Kavita Rao",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_T1_0801_10AM",
          "date": "2025-08-01",
          "start_time": "10:00 AM",
          "end_time": "11:00 AM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_T1_0801_03PM",
          "date": "2025-08-01",
          "start_time": "03:00 PM",
          "end_time": "04:00 PM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_T1_0802_11AM",
          "date": "2025-08-02",
          "start_time": "11:00 AM",
          "end_time": "12:00 PM",
          "timezone": "Asia/Kolkata"
        }
      ]
    },
    {
      "panelist_id": "panel_T2",
      "name": "Mr. Vikram Mehta",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_T2_0801_11AM",
          "date": "2025-08-01",
          "start_time": "11:00 AM",
          "end_time": "12:00 PM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_T2_0802_09AM",
          "date": "2025-08-02",
          "start_time": "09:00 AM",
          "end_time": "10:00 AM",
          "timezone": "Asia/Kolkata"
        }
      ]
    },
    {
      "panelist_id": "panel_HR1",
      "name": "Ms. Anjali Gupta",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_HR1_0801_02PM",
          "date": "2025-08-01",
          "start_time": "02:00 PM",
          "end_time": "02:45 PM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_HR1_0802_10AM",
          "date": "2025-08-02",
          "start_time": "10:00 AM",
          "end_time": "10:45 AM",
          "timezone": "Asia/Kolkata"
        }
      ]
    }
  ]
}
```

### 6.2. Interview Scheduling Process

#### 1. Job Queue (Celery)

- The microservice will maintain an internal Celery queue for `scheduling_tasks`.
- Each `scheduling_task` will correspond to a `CandidateStatus` record.
- When a candidate is ready for a call, a `scheduling_task` will be added to the queue. Initially, all candidates from the seeded data will be added.

#### 2. Worker Logic

- A Celery worker will fetch a `scheduling_task` from the queue.
- It will retrieve the `CandidateStatus` record from its MongoDB.
- **Panelist and Slot Selection:**
  - The worker will iterate through the list of `Panelist` records (fetched from its MongoDB) based on a simple round-robin approach or internal tracking to ensure load balancing.
  - For the *current candidate*, it will pick the *first available slot* from the *currently selected panelist*.
  - An "available slot" is one where `is_booked` is `false`.
  - If the currently selected panelist has no available slots left, the worker will move to the next panelist in the sorted list of panelists (e.g., sorted by `panelist_id` or `name` for consistent round-robin).
  - If no panelist has available slots for the candidate, the candidate's `status` will be updated to `MAX_RETRIES_EXCEEDED` and the task will complete.
- **Plivo Call Initiation (via Agent Flow API):**
  - If a suitable panelist and available slots are found, the worker will prepare the `callData` payload for Plivo's Agent Flow API.
  - The `callData` will include:
    - `to`: The candidate's phone number (formatted to E.164).
    - `webhook_endpoint`: The URL of the microservice's Plivo webhook endpoint (e.g., `http://your-local-ip:8000/plivo/status-callback`).
    - `variables`: A JSON object containing dynamic data for the Plivo Agent Flow. This will include:
      - `candidate_name`: Candidate's name.
      - `company_name`: Company name.
      - `round_info`: Interview round type.
      - `job_id`: Job ID.
      - `panelist_name`: Name of the selected panelist.
      - `available_slots_data`: A structured representation of all available slots for the selected panelist to be presented to the candidate (e.g., a list of {`dtmf_digit`: "1", `display_text`: "August 1st, 10 AM", `slot_id`: "slot_T1_0801_10AM"}). This will be dynamically generated.
  - The worker will make an HTTP POST request to `https://agentflow.plivo.com/v1/account/{PLIVO_ACCOUNT_ID}/flow/{PLIVO_INTERVIEW_FLOW_ID}`.
  - Update `CandidateStatus.status` to `CALL_INITIATED` and `last_attempted_at`.

#### 3. Plivo Webhook Handling

- The microservice will expose a webhook endpoint (`POST /plivo/status-callback`) for Plivo to send call status updates and DTMF input from the candidate.
- This webhook will receive a JSON payload from Plivo (example fields: `CallUUID`, `From`, `To`, `CallStatus`, `HangupCause`, `Digits` (for DTMF input), `CustomData` if set by Agent Flow).
- **Processing:**
  - Identify the `CandidateStatus` record based on the `To` phone number.
  - **`INTERVIEW_SCHEDULED`:** If the candidate successfully selects a slot (extracted DTMF input maps to a `slot_id` from `available_slots_data`):
    - Update `CandidateStatus.status` to `INTERVIEW_SCHEDULED`.
    - Find the `Panelist` and their specific `slot_id`. Update that slot by setting `is_booked: true`, `booked_candidate_id`, and `booked_interview_id`.
    - Create a new `Interview` record, linking it to the candidate and panelist(s), and storing the scheduled time.
    - **Advance Panelist:** For the next scheduling attempt, the system will move to the *next candidate* and the *next panelist* in the array to ensure load balancing and fair distribution.
  - **`RETRY_REQUIRED`:** If `CallStatus` is "no-answer", "busy", "failed", or `HangupCause` indicates unreachability/busy, OR if the candidate explicitly chooses "call later" via DTMF:
    - Increment `CandidateStatus.interview_attempts`.
    - If `CandidateStatus.interview_attempts < 3` (maximum 3 retries): Set `CandidateStatus.status` to `RETRY_REQUIRED`. Add a new `scheduling_task` to the Celery queue for the same candidate with a 30-minute delay.
    - If `CandidateStatus.interview_attempts == 3`: Set `CandidateStatus.status` to `MAX_RETRIES_EXCEEDED`.
    - **Maintain Panelist:** For the next scheduling attempt, the system will attempt to schedule the *next candidate* with the *current panelist* (if they have other available slots). This "sticky panelist" approach aims to fill a panelist's slots if they are available for multiple candidates.
  - **`NOT_INTERESTED`:** If the candidate explicitly chooses "not interested" via DTMF:
    - Set `CandidateStatus.status` to `NOT_INTERESTED`.
    - No further retries.
    - **Advance Panelist:** Move to the *next candidate* and *next panelist*.
  - **Error/Invalid Input:** If Plivo returns an error or invalid DTMF, the logic will default to `RETRY_REQUIRED` if attempts remain, otherwise `MAX_RETRIES_EXCEEDED`.

### 6.3. Load Balancing Logic (Simplified MVP)

- **Candidate Queue:** Candidates are processed from a queue.
- **Panelist Rotation:** The system will keep track of the "currently selected panelist". After an interview is successfully scheduled with this panelist, the system moves to the next panelist in a defined order (e.g., alphabetical by `panelist_id`). If an interview is *not* scheduled (retry, not interested), the system attempts the next candidate with the *same panelist* (if they still have available slots) to fill up that panelist's schedule first, before moving to the next panelist only if the current one has no more slots.

## 7. Non-Functional Requirements

- **Performance:** The system should be able to handle a moderate volume of concurrent call attempts. Celery for background processing helps manage load.
- **Scalability:** Initial deployment is local. Future considerations for scaling will involve Dockerization and orchestration (e.g., Kubernetes).
- **Reliability:** Automated retries for failed calls (up to 3 attempts). Idempotent webhook handling where possible.
- **Security:** Basic environment variable management. No external APIs beyond Plivo are exposed or consumed currently.
- **Observability:** Console logging for operational insights.
- **Maintainability:** Clean, modular Python code following FastAPI best practices. Pydantic for data validation.

## 8. Technical Design Details

### 8.1. FastAPI Endpoints

- `POST /api/v1/start-scheduling`: Receives the initial payload to seed the DB and trigger scheduling jobs.
  - **Input:** JSON payload as defined in Section 6.1.
  - **Output:** HTTP 202 Accepted, with a message indicating successful receipt and initiation of scheduling.
- `POST /plivo/status-callback`: Webhook endpoint for Plivo to send call status and DTMF input.
  - **Input:** Plivo-specific JSON payload (containing `CallUUID`, `From`, `To`, `CallStatus`, `HangupCause`, `Digits` for DTMF input, etc., and potentially custom variables defined in the Agent Flow).
  - **Output:** HTTP 200 OK.

### 8.2. Celery Configuration

- Broker: Redis (local Docker container).
- Backend: Redis.
- Task: `schedule_interview_task` will be the main Celery task responsible for picking a candidate, selecting a panelist/slot, and initiating the Plivo call.

### 8.3. Plivo Agent Flow Design (Conceptual)

The Plivo Agent Flow will be pre-configured on the Plivo dashboard. The Python service will trigger this flow, passing dynamic data via the `variables` field in the API call.

**Flow Steps:**

1. **Welcome & Context:**
   - `Speak`: "Hello [candidate_name], this is [company_name] calling to schedule your [round_info] interview for the [job_id] position."
   - `Speak`: "Your interview will be with [panelist_name]."

2. **Present Options:**
   - `Speak`: "We have the following time slots available for you. Please listen carefully and press the number corresponding to your preferred option:"
   - `Loop`: Iterate through `available_slots_data` provided in `variables`. For each slot:
     - `Speak`: "For [display_text], press [dtmf_digit]." (e.g., "For August 1st, 10 AM, press 1.")
   - `Speak`: "To repeat these options, press 0."
   - `Speak`: "If you are not interested in this interview, press 9."
   - `Speak`: "If you are currently busy and would like us to call you back later, press 8."

3. **Capture Input:**
   - `GetDigits`: Collect DTMF input from the candidate. Set a timeout.
   - `On No Input/Timeout`: Redirect to the "Call Later" path or retry options.

4. **Process Input (Internal Logic of Agent Flow):**
   - `Conditional Branching`: Based on `Digits` received.
   - **If Slot Selected (1, 2, 3...):**
     - `Speak`: "You have selected [Selected Date] at [Selected Time]. To confirm, press 1. To go back and select a different slot, press 2."
     - `GetDigits`: Collect confirmation (1 or 2).
     - `On Confirmation (1)`:
       - `Set Custom Data`: Store the selected `slot_id` (from `available_slots_data` map) into a custom Plivo variable (e.g., `selected_slot_id`).
       - `Webhook`: Trigger webhook to `PLIVO_WEBHOOK_URL` with `CallStatus: completed`, `Digits: <selected_dtmf_digit>`, and `CustomData: {selected_slot_id: "..."}`.
       - `Speak`: "Thank you, your interview is scheduled. Goodbye."
       - `Hangup`.
     - `On Go Back (2)`: Redirect back to "Present Options" step.
   - **If Repeat Options (0):** Redirect back to "Present Options" step.
   - **If Call Later (8):**
     - `Webhook`: Trigger webhook to `PLIVO_WEBHOOK_URL` with `CallStatus: completed`, `Digits: 8`, `CustomData: {outcome: "busy"}`.
     - `Speak`: "We understand you're busy. We will try calling you back in about 30 minutes. Goodbye."
     - `Hangup`.
   - **If Not Interested (9):**
     - `Webhook`: Trigger webhook to `PLIVO_WEBHOOK_URL` with `CallStatus: completed`, `Digits: 9`, `CustomData: {outcome: "not_interested"}`.
     - `Speak`: "We understand. Thank you for your time. Goodbye."
     - `Hangup`.
   - **Unhandled/Invalid Digit:**
     - `Speak`: "Your input was not valid. Please try again."
     - `Redirect`: Back to "Present Options" or "Call Later" if multiple invalid attempts.

This detailed flow must be built and configured directly in the Plivo Agent Flow builder on their dashboard. The Python service's role is primarily to *trigger* this flow with the correct dynamic data (`variables`) and then *listen* to the resulting webhooks.

### 8.4. Environment Variables

- `MONGO_URI`: MongoDB connection string for the microservice's DB.
- `REDIS_HOST`: Redis host for Celery.
- `REDIS_PORT`: Redis port for Celery.
- `PLIVO_ACCOUNT_ID`: Your unique Plivo account identifier.
- `PLIVO_INTERVIEW_FLOW_ID`: The ID of your pre-configured Plivo Agent Flow for interview scheduling.
- `PLIVO_WEBHOOK_URL`: Base URL of the microservice's webhook endpoint for Plivo callbacks (e.g., `http://your-local-ip:8000/plivo/status-callback`).

## 9. Future Considerations (Post-MVP)

- **Veda Integration:** Expose APIs for Veda to trigger scheduling and receive status updates. This would involve Veda making HTTP calls to `POST /api/v1/start-scheduling` (or a more specific endpoint for individual candidate triggers) and ISM making HTTP calls to Veda's webhook endpoints for status updates.
- **Recruiter UI for Panelists:** Develop a UI for recruiters to manage panelist availability and roles.
- **Candidate Self-Service Rescheduling Portal:** A web portal for candidates to reschedule their interviews.
- **Meeting Link Generation:** Integrate with Google Calendar API or similar to generate and send meeting links.
- **Email/SMS Notifications:** Send automated confirmations and reminders.
- **Advanced Scheduling Logic:** Prioritize slots based on business rules, optimize for panelist load, etc.