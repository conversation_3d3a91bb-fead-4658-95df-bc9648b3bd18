version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: interview_scheduling_mongo
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: interview_scheduling

  redis:
    image: redis:7.2-alpine
    container_name: interview_scheduling_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
  redis_data: