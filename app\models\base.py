"""
Base model classes for MongoDB integration.

Provides common functionality for all database models including
ObjectId handling, timestamps, and serialization.
"""

from datetime import datetime
from typing import Optional, Any, Dict

from bson import ObjectId
from pydantic import BaseModel, Field, validator


class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic models."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)
    
    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")


class BaseMongoModel(BaseModel):
    """
    Base model for MongoDB documents.
    
    Provides common fields and functionality for all MongoDB models
    including ObjectId handling and timestamp management.
    """
    
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        """Pydantic configuration for MongoDB models."""
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
        
    def dict(self, **kwargs) -> Dict[str, Any]:
        """
        Override dict method to handle ObjectId serialization.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the model
        """
        d = super().dict(**kwargs)
        if "_id" in d and d["_id"] is not None:
            d["_id"] = str(d["_id"])
        return d
    
    def json(self, **kwargs) -> str:
        """
        Override json method to handle ObjectId serialization.
        
        Returns:
            str: JSON representation of the model
        """
        return super().json(**kwargs)


class BaseCreateModel(BaseModel):
    """
    Base model for create operations.
    
    Excludes fields that should be auto-generated like id and timestamps.
    """
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class BaseUpdateModel(BaseModel):
    """
    Base model for update operations.
    
    All fields are optional to support partial updates.
    """
    
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True
