# Interview Scheduling Microservice - Context

## Business Context
This microservice automates interview scheduling by calling candidates via Plivo's voice API, presenting available time slots, and booking interviews based on candidate responses.

## Technical Context
- **Framework**: FastAPI (async Python web framework)
- **Database**: MongoDB (document-based, stores candidate status, panelists, interviews)
- **Task Queue**: Celery with Redis broker (handles async call processing)
- **External API**: Plivo Agent Flow API (voice calls and DTMF processing)
- **Architecture**: Microservice with clean separation of concerns

## Data Flow
1. POST to `/api/v1/start-scheduling` seeds database with candidates and panelists
2. Celery tasks process candidates one by one
3. Each task selects next available panelist/slot and initiates Plivo call
4. Plivo Agent Flow guides candidate through slot selection via voice prompts
5. Webhook receives candidate's DTMF response and updates database accordingly
6. System continues with next candidate or retries based on response

## Key Business Rules
- Maximum 3 call attempts per candidate
- 30-minute delay between retry attempts
- Round-robin panelist selection for load balancing
- First-available slot selection within chosen panelist
- Automatic status transitions based on call outcomes

## Integration Points
- **Plivo Agent Flow**: Pre-configured voice workflow on Plivo dashboard
- **MongoDB**: Local instance for data persistence
- **Redis**: Local instance for Celery task queue
- **Webhook Endpoint**: Receives Plivo call status and DTMF input

## Current Scope (MVP)
- Standalone operation (no external system integration)
- Basic scheduling logic (no skill-based matching)
- Simple retry mechanism
- Console logging for monitoring
- Local deployment only

## Future Considerations
- Integration with main Veda platform
- Advanced scheduling algorithms
- Email/SMS notifications
- Candidate self-service portal
- Meeting link generation