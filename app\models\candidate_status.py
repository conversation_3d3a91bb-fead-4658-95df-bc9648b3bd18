"""
CandidateStatus model for Interview Scheduling Microservice.

Represents the current state of a candidate within the interview
scheduling process with proper validation and type hints.
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import Field, validator

from .base import BaseMongoModel, BaseCreateModel, BaseUpdateModel, PyObjectId


class CandidateStatusEnum(str, Enum):
    """Enumeration of possible candidate statuses."""
    
    PENDING_CALL = "PENDING_CALL"
    CALL_INITIATED = "CALL_INITIATED"
    INTERVIEW_SCHEDULED = "INTERVIEW_SCHEDULED"
    RETRY_REQUIRED = "RETRY_REQUIRED"
    NOT_INTERESTED = "NOT_INTERESTED"
    MAX_RETRIES_EXCEEDED = "MAX_RETRIES_EXCEEDED"


class CandidateStatus(BaseMongoModel):
    """
    CandidateStatus model representing a candidate's scheduling status.
    
    Stores the current state of a candidate within the interview
    scheduling process including contact information, status, and
    scheduling attempts.
    """
    
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    name: str = Field(..., description="Candi<PERSON>'s full name")
    phone_number: str = Field(
        ..., 
        description="Candidate's phone number in E.164 format (e.g., +919876543210)"
    )
    email: str = Field(..., description="Candidate's email address")
    job_id: str = Field(..., description="Job identifier")
    round_info: str = Field(..., description="Interview round type (e.g., 'technical', 'HR')")
    company_name: str = Field(..., description="Company name")
    status: CandidateStatusEnum = Field(
        default=CandidateStatusEnum.PENDING_CALL,
        description="Current status of the candidate"
    )
    interview_attempts: int = Field(
        default=0,
        description="Number of call attempts made",
        ge=0
    )
    last_attempted_at: Optional[datetime] = Field(
        default=None,
        description="Timestamp of the last call attempt"
    )
    scheduled_interview_id: Optional[PyObjectId] = Field(
        default=None,
        description="Reference to Interview record if scheduled"
    )
    
    @validator("phone_number")
    def validate_phone_number(cls, v: str) -> str:
        """
        Validate phone number format.
        
        Args:
            v: Phone number string
            
        Returns:
            str: Validated phone number
            
        Raises:
            ValueError: If phone number format is invalid
        """
        if not v.startswith("+"):
            raise ValueError("Phone number must be in E.164 format (start with +)")
        
        # Remove the + and check if remaining characters are digits
        digits = v[1:]
        if not digits.isdigit():
            raise ValueError("Phone number must contain only digits after +")
        
        if len(digits) < 10 or len(digits) > 15:
            raise ValueError("Phone number must be between 10 and 15 digits")
        
        return v
    
    @validator("email")
    def validate_email(cls, v: str) -> str:
        """
        Basic email validation.
        
        Args:
            v: Email string
            
        Returns:
            str: Validated email
            
        Raises:
            ValueError: If email format is invalid
        """
        if "@" not in v or "." not in v.split("@")[-1]:
            raise ValueError("Invalid email format")
        return v.lower()
    
    class Config:
        """Pydantic configuration."""
        collection_name = "candidate_status"


class CandidateStatusCreate(BaseCreateModel):
    """Model for creating a new CandidateStatus record."""
    
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    name: str = Field(..., description="Candidate's full name")
    phone_number: str = Field(
        ..., 
        description="Candidate's phone number in E.164 format"
    )
    email: str = Field(..., description="Candidate's email address")
    job_id: str = Field(..., description="Job identifier")
    round_info: str = Field(..., description="Interview round type")
    company_name: str = Field(..., description="Company name")
    
    @validator("phone_number")
    def validate_phone_number(cls, v: str) -> str:
        """Validate phone number format."""
        if not v.startswith("+"):
            raise ValueError("Phone number must be in E.164 format (start with +)")
        
        digits = v[1:]
        if not digits.isdigit():
            raise ValueError("Phone number must contain only digits after +")
        
        if len(digits) < 10 or len(digits) > 15:
            raise ValueError("Phone number must be between 10 and 15 digits")
        
        return v
    
    @validator("email")
    def validate_email(cls, v: str) -> str:
        """Basic email validation."""
        if "@" not in v or "." not in v.split("@")[-1]:
            raise ValueError("Invalid email format")
        return v.lower()


class CandidateStatusUpdate(BaseUpdateModel):
    """Model for updating a CandidateStatus record."""
    
    status: Optional[CandidateStatusEnum] = None
    interview_attempts: Optional[int] = Field(default=None, ge=0)
    last_attempted_at: Optional[datetime] = None
    scheduled_interview_id: Optional[PyObjectId] = None
