"""
Database connection and management for Interview Scheduling Microservice.

Handles MongoDB connections using Motor (async MongoDB driver) with
proper connection pooling and error handling.
"""

import logging
from typing import Optional

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

logger = logging.getLogger(__name__)

# Global database connection
_client: Optional[AsyncIOMotorClient] = None
_database: Optional[AsyncIOMotorDatabase] = None


async def connect_to_mongo(mongo_uri: str, database_name: str = "interview_scheduling") -> None:
    """
    Connect to MongoDB using Motor async client.
    
    Args:
        mongo_uri: MongoDB connection URI
        database_name: Name of the database to connect to
        
    Raises:
        ConnectionFailure: If unable to connect to MongoDB
        ServerSelectionTimeoutError: If server selection times out
    """
    global _client, _database
    
    try:
        logger.info(f"Connecting to MongoDB at {mongo_uri}")
        
        # Create MongoDB client with connection pooling
        _client = AsyncIOMotorClient(
            mongo_uri,
            maxPoolSize=10,
            minPoolSize=1,
            maxIdleTimeMS=30000,
            waitQueueTimeoutMS=5000,
            serverSelectionTimeoutMS=5000,
        )
        
        # Test the connection
        await _client.admin.command("ping")
        
        # Get database reference
        _database = _client[database_name]
        
        # Create indexes for better performance
        await _create_indexes()
        
        logger.info(f"Successfully connected to MongoDB database: {database_name}")
        
    except (ConnectionFailure, ServerSelectionTimeoutError) as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error connecting to MongoDB: {e}")
        raise


async def close_mongo_connection() -> None:
    """
    Close the MongoDB connection.
    
    Should be called during application shutdown to properly
    close the connection pool.
    """
    global _client, _database
    
    if _client:
        logger.info("Closing MongoDB connection")
        _client.close()
        _client = None
        _database = None
        logger.info("MongoDB connection closed")


def get_database() -> AsyncIOMotorDatabase:
    """
    Get the MongoDB database instance.
    
    Returns:
        AsyncIOMotorDatabase: MongoDB database instance
        
    Raises:
        RuntimeError: If database connection is not established
    """
    if _database is None:
        raise RuntimeError("Database connection not established. Call connect_to_mongo() first.")
    return _database


async def _create_indexes() -> None:
    """
    Create database indexes for better query performance.
    
    Creates indexes on frequently queried fields to improve
    performance of database operations.
    """
    if _database is None:
        return
    
    try:
        # CandidateStatus collection indexes
        candidate_status_collection = _database.candidate_status
        await candidate_status_collection.create_index("candidate_id", unique=True)
        await candidate_status_collection.create_index("status")
        await candidate_status_collection.create_index("phone_number")
        await candidate_status_collection.create_index("last_attempted_at")
        
        # Panelist collection indexes
        panelist_collection = _database.panelist
        await panelist_collection.create_index("panelist_id", unique=True)
        await panelist_collection.create_index("available_slots.slot_id")
        await panelist_collection.create_index("available_slots.is_booked")
        
        # Interview collection indexes
        interview_collection = _database.interview
        await interview_collection.create_index("candidate_id")
        await interview_collection.create_index("panelist_ids")
        await interview_collection.create_index("status")
        await interview_collection.create_index("scheduled_time.date")
        
        logger.info("Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating database indexes: {e}")
        # Don't raise here as indexes are not critical for basic functionality


async def health_check() -> bool:
    """
    Check if the database connection is healthy.
    
    Returns:
        bool: True if database is accessible, False otherwise
    """
    try:
        if _client is None:
            return False
        
        # Ping the database
        await _client.admin.command("ping")
        return True
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False
