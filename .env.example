# Environment configuration for Interview Scheduling Microservice

# Application settings
ENVIRONMENT=development
DEBUG=true

# MongoDB settings
MONGO_URI=mongodb://localhost:27017/interview_scheduling
MONGO_DATABASE=interview_scheduling

# Redis settings (for Celery)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Celery settings (optional - will be built from Redis settings if not provided)
CELERY_BROKER_URL=
CELERY_RESULT_BACKEND=

# Plivo settings
PLIVO_ACCOUNT_ID=your_plivo_account_id
PLIVO_INTERVIEW_FLOW_ID=your_plivo_flow_id
PLIVO_WEBHOOK_URL=http://your-server:8000
PLIVO_AUTH_TOKEN=your_plivo_auth_token

# Interview scheduling settings
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_MINUTES=30