"""
Webhook service for Interview Scheduling Microservice.

Handles processing of Plivo webhooks including call status updates
and DTMF input from candidates.
"""

import logging
from typing import Dict, Any, Optional

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.v1.schemas import PlivoWebhookRequest
from app.models.candidate_status import CandidateStatusEnum
from app.services.scheduling_service import SchedulingService
from app.core.config import get_settings

logger = logging.getLogger(__name__)


class WebhookService:
    """
    Service for processing Plivo webhooks.
    
    Handles call status updates and DTMF input processing
    to manage the interview scheduling workflow.
    """
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """
        Initialize the webhook service.
        
        Args:
            database: MongoDB database instance
        """
        self.db = database
        self.settings = get_settings()
        self.candidate_collection = database.candidate_status
        self.scheduling_service = SchedulingService(database)
    
    async def process_plivo_webhook(self, webhook_data: PlivoWebhookRequest) -> Dict[str, Any]:
        """
        Process a Plivo webhook request.
        
        Analyzes the webhook data and takes appropriate action based on
        call status and DTMF input.
        
        Args:
            webhook_data: Plivo webhook request data
            
        Returns:
            Dict[str, Any]: Processing result information
        """
        try:
            # Find candidate by phone number
            candidate = await self._find_candidate_by_phone(webhook_data.To)
            if not candidate:
                logger.warning(f"No candidate found for phone number {webhook_data.To}")
                return {"action": "ignored", "reason": "candidate_not_found"}
            
            candidate_id = candidate["candidate_id"]
            logger.info(f"Processing webhook for candidate {candidate_id}")
            
            # Determine action based on webhook data
            if webhook_data.Digits:
                # DTMF input received
                return await self._process_dtmf_input(candidate_id, webhook_data)
            elif webhook_data.CallStatus:
                # Call status update
                return await self._process_call_status(candidate_id, webhook_data)
            else:
                logger.warning(f"Webhook received without actionable data: {webhook_data}")
                return {"action": "ignored", "reason": "no_actionable_data"}
                
        except Exception as e:
            logger.error(f"Error processing Plivo webhook: {e}")
            raise
    
    async def _find_candidate_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Find a candidate by their phone number.
        
        Args:
            phone_number: Phone number to search for
            
        Returns:
            Optional[Dict[str, Any]]: Candidate data if found, None otherwise
        """
        try:
            candidate = await self.candidate_collection.find_one({
                "phone_number": phone_number
            })
            return candidate
        except Exception as e:
            logger.error(f"Error finding candidate by phone {phone_number}: {e}")
            return None
    
    async def _process_dtmf_input(
        self,
        candidate_id: str,
        webhook_data: PlivoWebhookRequest
    ) -> Dict[str, Any]:
        """
        Process DTMF input from candidate.
        
        Args:
            candidate_id: ID of the candidate
            webhook_data: Webhook data containing DTMF input
            
        Returns:
            Dict[str, Any]: Processing result
        """
        try:
            digits = webhook_data.Digits
            logger.info(f"Processing DTMF input '{digits}' for candidate {candidate_id}")
            
            # Parse DTMF input
            if digits == "9":
                # Not interested
                await self.scheduling_service.update_candidate_status(
                    candidate_id,
                    CandidateStatusEnum.NOT_INTERESTED
                )
                return {"action": "not_interested", "candidate_id": candidate_id}
                
            elif digits == "8":
                # Call later (retry required)
                candidate = await self.candidate_collection.find_one({
                    "candidate_id": candidate_id
                })
                
                if candidate and candidate.get("interview_attempts", 0) < self.settings.max_retry_attempts:
                    await self.scheduling_service.update_candidate_status(
                        candidate_id,
                        CandidateStatusEnum.RETRY_REQUIRED,
                        increment_attempts=True
                    )
                    # TODO: Schedule retry task with delay
                    return {"action": "retry_scheduled", "candidate_id": candidate_id}
                else:
                    await self.scheduling_service.update_candidate_status(
                        candidate_id,
                        CandidateStatusEnum.MAX_RETRIES_EXCEEDED
                    )
                    return {"action": "max_retries_exceeded", "candidate_id": candidate_id}
                    
            elif digits in ["1", "2", "3", "4", "5", "6", "7"]:
                # Slot selection
                return await self._process_slot_selection(candidate_id, digits, webhook_data)
                
            else:
                logger.warning(f"Unhandled DTMF input '{digits}' for candidate {candidate_id}")
                return {"action": "invalid_input", "candidate_id": candidate_id, "digits": digits}
                
        except Exception as e:
            logger.error(f"Error processing DTMF input: {e}")
            raise
    
    async def _process_slot_selection(
        self,
        candidate_id: str,
        slot_digit: str,
        webhook_data: PlivoWebhookRequest
    ) -> Dict[str, Any]:
        """
        Process slot selection from DTMF input.
        
        Args:
            candidate_id: ID of the candidate
            slot_digit: DTMF digit representing slot selection
            webhook_data: Webhook data
            
        Returns:
            Dict[str, Any]: Processing result
        """
        try:
            # For MVP, we'll implement a simplified slot selection
            # In a full implementation, this would map DTMF digits to specific slots
            # based on the data sent to Plivo Agent Flow
            
            # Find available panelist and slot
            slot_info = await self.scheduling_service.find_available_panelist_and_slot(candidate_id)
            
            if not slot_info:
                # No slots available, mark as max retries exceeded
                await self.scheduling_service.update_candidate_status(
                    candidate_id,
                    CandidateStatusEnum.MAX_RETRIES_EXCEEDED
                )
                return {"action": "no_slots_available", "candidate_id": candidate_id}
            
            # Book the interview slot
            interview_id = await self.scheduling_service.book_interview_slot(
                candidate_id,
                slot_info["panelist_id"],
                slot_info["slot_id"]
            )
            
            if interview_id:
                return {
                    "action": "interview_scheduled",
                    "candidate_id": candidate_id,
                    "interview_id": interview_id,
                    "panelist_id": slot_info["panelist_id"],
                    "slot_id": slot_info["slot_id"]
                }
            else:
                logger.error(f"Failed to book slot for candidate {candidate_id}")
                return {"action": "booking_failed", "candidate_id": candidate_id}
                
        except Exception as e:
            logger.error(f"Error processing slot selection: {e}")
            raise
    
    async def _process_call_status(
        self,
        candidate_id: str,
        webhook_data: PlivoWebhookRequest
    ) -> Dict[str, Any]:
        """
        Process call status updates.
        
        Args:
            candidate_id: ID of the candidate
            webhook_data: Webhook data containing call status
            
        Returns:
            Dict[str, Any]: Processing result
        """
        try:
            call_status = webhook_data.CallStatus
            hangup_cause = webhook_data.HangupCause
            
            logger.info(f"Processing call status '{call_status}' for candidate {candidate_id}")
            
            # Handle different call statuses
            if call_status in ["no-answer", "busy", "failed"] or hangup_cause in ["NO_ANSWER", "BUSY"]:
                # Call failed, schedule retry if attempts remaining
                candidate = await self.candidate_collection.find_one({
                    "candidate_id": candidate_id
                })
                
                if candidate and candidate.get("interview_attempts", 0) < self.settings.max_retry_attempts:
                    await self.scheduling_service.update_candidate_status(
                        candidate_id,
                        CandidateStatusEnum.RETRY_REQUIRED,
                        increment_attempts=True
                    )
                    # TODO: Schedule retry task with delay
                    return {"action": "retry_scheduled", "candidate_id": candidate_id, "reason": call_status}
                else:
                    await self.scheduling_service.update_candidate_status(
                        candidate_id,
                        CandidateStatusEnum.MAX_RETRIES_EXCEEDED
                    )
                    return {"action": "max_retries_exceeded", "candidate_id": candidate_id}
                    
            elif call_status == "completed":
                # Call completed successfully - DTMF processing should handle the rest
                return {"action": "call_completed", "candidate_id": candidate_id}
                
            else:
                logger.info(f"Call status '{call_status}' for candidate {candidate_id} - no action needed")
                return {"action": "status_logged", "candidate_id": candidate_id, "status": call_status}
                
        except Exception as e:
            logger.error(f"Error processing call status: {e}")
            raise
