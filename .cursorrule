# Interview Scheduling Microservice - Cursor Rules

## Project Context
You are building an Interview Scheduling Microservice (ISM) based on the provided PRD. This is a Python/FastAPI application that automates interview scheduling via Plivo phone calls.

## Core Technologies
- Python 3.11+
- FastAPI
- MongoDB (Motor/PyMongo)
- Celery + Redis
- Pydantic for validation
- Plivo API integration

## Architecture Principles
1. Follow clean architecture patterns
2. Use dependency injection for database and external services
3. Implement proper error handling and logging
4. Write type hints for all functions
5. Use Pydantic models for all data validation
6. Follow FastAPI best practices

## Code Style & Standards
- Use Python black for formatting
- Follow PEP 8 naming conventions
- Use descriptive variable and function names
- Add docstrings to all classes and functions
- Use async/await patterns consistently
- Implement proper exception handling

## File Organization
- Models: `/app/models/` (Pydantic + MongoDB models)
- Routers: `/app/routers/` (FastAPI route handlers)
- Services: `/app/services/` (Business logic)
- Tasks: `/app/tasks/` (Celery tasks)
- Utils: `/app/utils/` (Helper functions)
- Config: `/app/config.py` (Settings and environment)

## Database Design
- Use MongoDB with Motor (async driver)
- Implement proper indexes for performance
- Use ObjectId for primary keys
- Follow the schema defined in PRD section 5

## API Design
- Follow RESTful conventions
- Use proper HTTP status codes
- Implement request/response validation
- Add comprehensive error responses
- Include proper API documentation

## External Integrations
- Plivo Agent Flow API integration
- Webhook handling for Plivo callbacks
- Celery task management
- Redis for caching and queues

## Testing Requirements
- Write unit tests for all services
- Mock external API calls
- Test webhook endpoints
- Test Celery tasks
- Use pytest framework

## Security Considerations
- Validate all inputs
- Use environment variables for secrets
- Implement proper CORS settings
- Add request rate limiting
- Secure webhook endpoints

## Error Handling
- Create custom exception classes
- Log all errors with context
- Return meaningful error messages
- Handle network failures gracefully
- Implement retry mechanisms

## Performance
- Use async operations where possible
- Implement database connection pooling
- Add caching for frequent queries
- Monitor task queue performance
- Optimize database queries

## Always Remember
1. Check the PRD for exact requirements before implementing
2. Follow the data models exactly as specified
3. Implement the Plivo integration as described
4. Use the specified environment variables
5. Follow the load balancing logic described
6. Implement proper status transitions