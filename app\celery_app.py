"""
Celery application configuration for Interview Scheduling Microservice.

Configures Celery for background task processing with Redis broker
and includes task definitions for interview scheduling.
"""

import logging
from celery import Celery

from app.core.config import get_settings

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create Celery app
celery_app = Celery(
    "interview_scheduling",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.tasks.scheduling_tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Task routing
celery_app.conf.task_routes = {
    "app.tasks.scheduling_tasks.schedule_interview_task": {"queue": "scheduling"},
    "app.tasks.scheduling_tasks.retry_interview_task": {"queue": "scheduling"},
}

if __name__ == "__main__":
    celery_app.start()
