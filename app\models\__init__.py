"""
Database models for Interview Scheduling Microservice.

Contains Pydantic models for MongoDB collections with proper
validation, type hints, and serialization.
"""

from .candidate_status import CandidateStatus, CandidateStatusCreate, CandidateStatusUpdate
from .panelist import Panelist, Panelist<PERSON>reate, PanelistUpdate, Slot
from .interview import Interview, InterviewCreate, InterviewUpdate, ScheduledTime

__all__ = [
    "CandidateStatus",
    "CandidateStatusCreate", 
    "CandidateStatusUpdate",
    "Panelist",
    "PanelistCreate",
    "PanelistUpdate",
    "Slot",
    "Interview",
    "InterviewCreate",
    "InterviewUpdate",
    "ScheduledTime",
]
