# Interview Scheduling Microservice - Architecture

## System Architecture

## Component Responsibilities

### FastAPI Application
- **Routes**: Handle HTTP requests for scheduling initialization and webhooks
- **Models**: Pydantic models for request/response validation
- **Services**: Business logic for candidate and panelist management
- **Database**: MongoDB connection and operations

### Celery Worker
- **Task Processing**: Execute interview scheduling tasks asynchronously
- **Plivo Integration**: Make API calls to initiate Agent Flows
- **Retry Logic**: <PERSON><PERSON> failed calls with exponential backoff
- **Status Updates**: Update candidate status based on call outcomes

### MongoDB Collections
- **CandidateStatus**: Track individual candidate scheduling progress
- **Panelist**: Store panelist information and availability slots
- **Interview**: Record successfully scheduled interviews

### Plivo Integration
- **Agent Flow**: Pre-configured voice workflow for candidate interaction
- **Webhooks**: Receive call status and DTMF input from Plivo
- **Variables**: Dynamic content passed to Agent Flow (slots, names, etc.)

## Data Flow Sequence

1. **Initialization**: POST /api/v1/start-scheduling receives candidate and panelist data
2. **Database Seeding**: Store candidates and panelists in MongoDB
3. **Task Queuing**: Create Celery tasks for each candidate
4. **Task Processing**: Worker picks up task, selects panelist/slot
5. **Call Initiation**: Worker calls Plivo Agent Flow API with dynamic variables
6. **Voice Interaction**: Plivo guides candidate through slot selection
7. **Webhook Processing**: Receive DTMF response and call status
8. **Status Update**: Update database based on candidate response
9. **Next Action**: Queue retry task or process next candidate

## Error Handling Strategy

- **Network Failures**: Retry with exponential backoff
- **Invalid Responses**: Log error and mark for manual review
- **Database Errors**: Rollback transactions and retry
- **Plivo API Errors**: Handle rate limits and service failures
- **Webhook Failures**: Implement idempotent processing

## Scalability Considerations

- **Horizontal Scaling**: Multiple Celery workers for parallel processing
- **Database Optimization**: Proper indexing and connection pooling
- **Rate Limiting**: Respect Plivo API rate limits
- **Monitoring**: Log all operations for debugging and analytics