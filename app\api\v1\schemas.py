"""
Request and response schemas for API v1 endpoints.

Contains Pydantic models for API request/response validation
and serialization.
"""

from typing import List, Optional

from pydantic import BaseModel, Field

from app.models.panelist import Slot


class CandidateRequest(BaseModel):
    """Schema for candidate data in start-scheduling request."""
    
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    name: str = Field(..., description="Candidate's full name")
    phone_number: str = Field(
        ..., 
        description="Candidate's phone number in E.164 format"
    )
    email: str = Field(..., description="Candidate's email address")


class PanelistRequest(BaseModel):
    """Schema for panelist data in start-scheduling request."""
    
    panelist_id: str = Field(..., description="Unique identifier for the panelist")
    name: str = Field(..., description="Panelist's full name")
    email: str = Field(..., description="Panelist's email address")
    available_slots: List[Slot] = Field(
        ...,
        description="Array of available time slots"
    )


class StartSchedulingRequest(BaseModel):
    """
    Schema for the start-scheduling endpoint request.
    
    Contains all data needed to initialize the scheduling process
    including candidates, panelists, and job information.
    """
    
    candidates: List[CandidateRequest] = Field(
        ...,
        description="List of candidates to schedule interviews for"
    )
    round_info: str = Field(..., description="Interview round type (e.g., 'technical', 'HR')")
    company_name: str = Field(..., description="Company name")
    job_id: str = Field(..., description="Job identifier")
    panelists: List[PanelistRequest] = Field(
        ...,
        description="List of panelists with their available slots"
    )


class StartSchedulingResponse(BaseModel):
    """Schema for the start-scheduling endpoint response."""
    
    message: str = Field(..., description="Response message")
    candidates_processed: int = Field(..., description="Number of candidates processed")
    panelists_processed: int = Field(..., description="Number of panelists processed")
    scheduling_initiated: bool = Field(..., description="Whether scheduling was initiated")


class PlivoWebhookRequest(BaseModel):
    """
    Schema for Plivo webhook request.
    
    Contains data sent by Plivo for call status updates and
    DTMF input from candidates.
    """
    
    CallUUID: Optional[str] = Field(default=None, description="Unique call identifier")
    From: Optional[str] = Field(default=None, description="Caller's phone number")
    To: Optional[str] = Field(default=None, description="Called phone number")
    CallStatus: Optional[str] = Field(default=None, description="Current call status")
    HangupCause: Optional[str] = Field(default=None, description="Reason for call hangup")
    Digits: Optional[str] = Field(default=None, description="DTMF digits pressed by candidate")
    CustomData: Optional[dict] = Field(default=None, description="Custom data from Agent Flow")
    
    # Additional fields that might be sent by Plivo
    Duration: Optional[str] = Field(default=None, description="Call duration")
    BillDuration: Optional[str] = Field(default=None, description="Billable duration")
    EndTime: Optional[str] = Field(default=None, description="Call end time")
    StartTime: Optional[str] = Field(default=None, description="Call start time")


class PlivoWebhookResponse(BaseModel):
    """Schema for Plivo webhook response."""
    
    status: str = Field(..., description="Processing status")
    message: str = Field(..., description="Response message")


class ErrorResponse(BaseModel):
    """Schema for error responses."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[dict] = Field(default=None, description="Additional error details")
