"""
Configuration management for Interview Scheduling Microservice.

Uses Pydantic Settings for environment variable management with
proper validation and type hints.
"""

from functools import lru_cache
from typing import Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    
    All settings are loaded from environment variables with proper
    validation and default values where appropriate.
    """
    
    # Application settings
    environment: str = Field(default="development", description="Application environment")
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # MongoDB settings
    mongo_uri: str = Field(
        default="mongodb://localhost:27017/interview_scheduling",
        description="MongoDB connection URI"
    )
    mongo_database: str = Field(
        default="interview_scheduling",
        description="MongoDB database name"
    )
    
    # Redis settings (for Celery)
    redis_host: str = Field(default="localhost", description="Redis host")
    redis_port: int = Field(default=6379, description="Redis port")
    redis_db: int = Field(default=0, description="Redis database number")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    
    # Celery settings
    celery_broker_url: Optional[str] = Field(default=None, description="Celery broker URL")
    celery_result_backend: Optional[str] = Field(default=None, description="Celery result backend URL")
    
    # Plivo settings
    plivo_account_id: str = Field(..., description="Plivo account ID")
    plivo_interview_flow_id: str = Field(..., description="Plivo Agent Flow ID for interviews")
    plivo_webhook_url: str = Field(..., description="Base URL for Plivo webhooks")
    plivo_auth_token: Optional[str] = Field(default=None, description="Plivo auth token")
    
    # Interview scheduling settings
    max_retry_attempts: int = Field(default=3, description="Maximum retry attempts for failed calls")
    retry_delay_minutes: int = Field(default=30, description="Delay between retry attempts in minutes")
    
    @validator("celery_broker_url", pre=True, always=True)
    def build_celery_broker_url(cls, v: Optional[str], values: dict) -> str:
        """Build Celery broker URL from Redis settings if not provided."""
        if v:
            return v
        
        redis_host = values.get("redis_host", "localhost")
        redis_port = values.get("redis_port", 6379)
        redis_db = values.get("redis_db", 0)
        redis_password = values.get("redis_password")
        
        if redis_password:
            return f"redis://:{redis_password}@{redis_host}:{redis_port}/{redis_db}"
        return f"redis://{redis_host}:{redis_port}/{redis_db}"
    
    @validator("celery_result_backend", pre=True, always=True)
    def build_celery_result_backend(cls, v: Optional[str], values: dict) -> str:
        """Build Celery result backend URL from Redis settings if not provided."""
        if v:
            return v
        
        redis_host = values.get("redis_host", "localhost")
        redis_port = values.get("redis_port", 6379)
        redis_db = values.get("redis_db", 0)
        redis_password = values.get("redis_password")
        
        if redis_password:
            return f"redis://:{redis_password}@{redis_host}:{redis_port}/{redis_db}"
        return f"redis://{redis_host}:{redis_port}/{redis_db}"
    
    @validator("plivo_webhook_url")
    def validate_webhook_url(cls, v: str) -> str:
        """Ensure webhook URL is properly formatted."""
        if not v.startswith(("http://", "https://")):
            raise ValueError("Webhook URL must start with http:// or https://")
        return v.rstrip("/")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.
    
    Uses LRU cache to avoid re-reading environment variables
    on every call.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()
