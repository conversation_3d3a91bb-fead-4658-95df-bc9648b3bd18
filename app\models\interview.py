"""
Interview model for Interview Scheduling Microservice.

Represents a successfully scheduled interview with all relevant
details including participants and timing.
"""

from datetime import date
from enum import Enum
from typing import List

from pydantic import BaseModel, Field, validator

from .base import BaseMongoModel, BaseCreateModel, BaseUpdateModel


class InterviewStatusEnum(str, Enum):
    """Enumeration of possible interview statuses."""
    
    SCHEDULED = "SCHEDULED"
    CANCELLED = "CANCELLED"
    COMPLETED = "COMPLETED"
    NO_SHOW = "NO_SHOW"


class ScheduledTime(BaseModel):
    """
    Represents the scheduled time for an interview.
    
    Contains all timing information including date, start/end times,
    and timezone information.
    """
    
    date: date = Field(..., description="Date of the interview (YYYY-MM-DD)")
    start_time: str = Field(..., description="Start time (HH:MM AM/PM)")
    end_time: str = Field(..., description="End time (HH:MM AM/PM)")
    timezone: str = Field(default="Asia/Kolkata", description="Timezone for the interview")
    
    @validator("start_time", "end_time")
    def validate_time_format(cls, v: str) -> str:
        """
        Validate time format (HH:MM AM/PM).
        
        Args:
            v: Time string
            
        Returns:
            str: Validated time string
            
        Raises:
            ValueError: If time format is invalid
        """
        try:
            # Try to parse the time to validate format
            from datetime import time
            time.strptime(v, "%I:%M %p")
            return v
        except ValueError:
            raise ValueError("Time must be in HH:MM AM/PM format (e.g., '10:00 AM')")
    
    @validator("timezone")
    def validate_timezone(cls, v: str) -> str:
        """
        Validate timezone format.
        
        Args:
            v: Timezone string
            
        Returns:
            str: Validated timezone string
        """
        # Basic validation - could be enhanced with pytz
        if "/" not in v:
            raise ValueError("Timezone must be in format 'Continent/City' (e.g., 'Asia/Kolkata')")
        return v


class Interview(BaseMongoModel):
    """
    Interview model representing a scheduled interview.
    
    Stores details of a successfully scheduled interview including
    participants, timing, and current status.
    """
    
    candidate_id: str = Field(..., description="References CandidateStatus.candidate_id")
    panelist_ids: List[str] = Field(
        ...,
        description="References Panelist.panelist_id for all panelists involved"
    )
    job_id: str = Field(..., description="Job identifier")
    round_info: str = Field(..., description="Interview round type")
    company_name: str = Field(..., description="Company name")
    scheduled_time: ScheduledTime = Field(..., description="Scheduled time details")
    status: InterviewStatusEnum = Field(
        default=InterviewStatusEnum.SCHEDULED,
        description="Current status of the interview"
    )
    
    @validator("panelist_ids")
    def validate_panelist_ids_not_empty(cls, v: List[str]) -> List[str]:
        """
        Ensure at least one panelist is assigned.
        
        Args:
            v: List of panelist IDs
            
        Returns:
            List[str]: Validated list of panelist IDs
            
        Raises:
            ValueError: If no panelists are provided
        """
        if not v:
            raise ValueError("At least one panelist must be assigned to an interview")
        return v
    
    @validator("panelist_ids")
    def validate_unique_panelist_ids(cls, v: List[str]) -> List[str]:
        """
        Ensure all panelist IDs are unique.
        
        Args:
            v: List of panelist IDs
            
        Returns:
            List[str]: Validated list of panelist IDs
            
        Raises:
            ValueError: If duplicate panelist IDs are found
        """
        if len(v) != len(set(v)):
            raise ValueError("All panelist IDs must be unique")
        return v
    
    class Config:
        """Pydantic configuration."""
        collection_name = "interview"


class InterviewCreate(BaseCreateModel):
    """Model for creating a new Interview record."""
    
    candidate_id: str = Field(..., description="References CandidateStatus.candidate_id")
    panelist_ids: List[str] = Field(
        ...,
        description="References Panelist.panelist_id for all panelists involved"
    )
    job_id: str = Field(..., description="Job identifier")
    round_info: str = Field(..., description="Interview round type")
    company_name: str = Field(..., description="Company name")
    scheduled_time: ScheduledTime = Field(..., description="Scheduled time details")
    
    @validator("panelist_ids")
    def validate_panelist_ids_not_empty(cls, v: List[str]) -> List[str]:
        """Ensure at least one panelist is assigned."""
        if not v:
            raise ValueError("At least one panelist must be assigned to an interview")
        return v
    
    @validator("panelist_ids")
    def validate_unique_panelist_ids(cls, v: List[str]) -> List[str]:
        """Ensure all panelist IDs are unique."""
        if len(v) != len(set(v)):
            raise ValueError("All panelist IDs must be unique")
        return v


class InterviewUpdate(BaseUpdateModel):
    """Model for updating an Interview record."""
    
    status: InterviewStatusEnum = None
    scheduled_time: ScheduledTime = None
