"""
Scheduling service for Interview Scheduling Microservice.

Contains core business logic for interview scheduling including
panelist selection, slot management, and Celery task management.
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any

from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import ASCENDING

from app.models import CandidateStatus, Panelist, Interview, InterviewCreate, ScheduledTime
from app.models.candidate_status import CandidateStatusEnum
from app.models.interview import InterviewStatusEnum
from app.core.config import get_settings

logger = logging.getLogger(__name__)


class SchedulingService:
    """
    Service for managing interview scheduling operations.
    
    Handles the core business logic for scheduling interviews including
    panelist selection, slot booking, and status management.
    """
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """
        Initialize the scheduling service.
        
        Args:
            database: MongoDB database instance
        """
        self.db = database
        self.settings = get_settings()
        self.candidate_collection = database.candidate_status
        self.panelist_collection = database.panelist
        self.interview_collection = database.interview
    
    async def initiate_scheduling_for_all_candidates(self) -> None:
        """
        Initiate scheduling for all candidates with PENDING_CALL status.
        
        This method finds all candidates that need to be called and
        adds them to the Celery queue for processing.
        """
        try:
            # Find all candidates with PENDING_CALL status
            candidates = await self.candidate_collection.find({
                "status": CandidateStatusEnum.PENDING_CALL
            }).to_list(None)
            
            logger.info(f"Found {len(candidates)} candidates ready for scheduling")
            
            # For now, we'll simulate adding to Celery queue
            # In a full implementation, this would use Celery tasks
            for candidate in candidates:
                logger.info(f"Would add candidate {candidate['candidate_id']} to Celery queue")
                # TODO: Add to Celery queue when Celery is implemented
                # schedule_interview_task.delay(candidate['candidate_id'])
            
        except Exception as e:
            logger.error(f"Error initiating scheduling for candidates: {e}")
            raise
    
    async def find_available_panelist_and_slot(self, candidate_id: str) -> Optional[Dict[str, Any]]:
        """
        Find an available panelist and slot for a candidate.
        
        Implements round-robin panelist selection and finds the first
        available slot for the selected panelist.
        
        Args:
            candidate_id: ID of the candidate to schedule
            
        Returns:
            Optional[Dict[str, Any]]: Dictionary containing panelist and slot info,
                                   or None if no slots available
        """
        try:
            # Get all panelists sorted by panelist_id for consistent round-robin
            panelists = await self.panelist_collection.find({}).sort("panelist_id", ASCENDING).to_list(None)
            
            if not panelists:
                logger.warning("No panelists found in database")
                return None
            
            # Simple round-robin: for MVP, we'll just iterate through panelists
            for panelist_data in panelists:
                panelist = Panelist(**panelist_data)
                available_slots = panelist.get_available_slots()
                
                if available_slots:
                    # Return the first available slot
                    selected_slot = available_slots[0]
                    return {
                        "panelist": panelist,
                        "slot": selected_slot,
                        "panelist_id": panelist.panelist_id,
                        "slot_id": selected_slot.slot_id
                    }
            
            logger.warning("No available slots found across all panelists")
            return None
            
        except Exception as e:
            logger.error(f"Error finding available panelist and slot: {e}")
            raise
    
    async def book_interview_slot(
        self,
        candidate_id: str,
        panelist_id: str,
        slot_id: str
    ) -> Optional[str]:
        """
        Book an interview slot for a candidate.
        
        Creates an Interview record and updates the panelist's slot
        as booked.
        
        Args:
            candidate_id: ID of the candidate
            panelist_id: ID of the panelist
            slot_id: ID of the slot to book
            
        Returns:
            Optional[str]: Interview ID if successful, None otherwise
        """
        try:
            # Get candidate details
            candidate_data = await self.candidate_collection.find_one({
                "candidate_id": candidate_id
            })
            
            if not candidate_data:
                logger.error(f"Candidate {candidate_id} not found")
                return None
            
            # Get panelist and slot details
            panelist_data = await self.panelist_collection.find_one({
                "panelist_id": panelist_id
            })
            
            if not panelist_data:
                logger.error(f"Panelist {panelist_id} not found")
                return None
            
            panelist = Panelist(**panelist_data)
            slot = panelist.get_slot_by_id(slot_id)
            
            if not slot or slot.is_booked:
                logger.error(f"Slot {slot_id} not available for panelist {panelist_id}")
                return None
            
            # Create interview record
            scheduled_time = ScheduledTime(
                date=slot.date,
                start_time=slot.start_time,
                end_time=slot.end_time,
                timezone=slot.timezone
            )
            
            interview_create = InterviewCreate(
                candidate_id=candidate_id,
                panelist_ids=[panelist_id],
                job_id=candidate_data["job_id"],
                round_info=candidate_data["round_info"],
                company_name=candidate_data["company_name"],
                scheduled_time=scheduled_time
            )
            
            # Insert interview record
            interview_dict = interview_create.dict()
            interview_dict["status"] = InterviewStatusEnum.SCHEDULED
            result = await self.interview_collection.insert_one(interview_dict)
            interview_id = result.inserted_id
            
            # Update panelist slot as booked
            await self.panelist_collection.update_one(
                {
                    "panelist_id": panelist_id,
                    "available_slots.slot_id": slot_id
                },
                {
                    "$set": {
                        "available_slots.$.is_booked": True,
                        "available_slots.$.booked_candidate_id": candidate_id,
                        "available_slots.$.booked_interview_id": interview_id,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            # Update candidate status
            await self.candidate_collection.update_one(
                {"candidate_id": candidate_id},
                {
                    "$set": {
                        "status": CandidateStatusEnum.INTERVIEW_SCHEDULED,
                        "scheduled_interview_id": interview_id,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            logger.info(f"Successfully booked interview {interview_id} for candidate {candidate_id} "
                       f"with panelist {panelist_id} at slot {slot_id}")
            
            return str(interview_id)
            
        except Exception as e:
            logger.error(f"Error booking interview slot: {e}")
            raise
    
    async def update_candidate_status(
        self,
        candidate_id: str,
        status: CandidateStatusEnum,
        increment_attempts: bool = False
    ) -> bool:
        """
        Update a candidate's status and optionally increment attempts.
        
        Args:
            candidate_id: ID of the candidate
            status: New status to set
            increment_attempts: Whether to increment interview_attempts
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            update_data = {
                "status": status,
                "last_attempted_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            if increment_attempts:
                update_data["$inc"] = {"interview_attempts": 1}
                result = await self.candidate_collection.update_one(
                    {"candidate_id": candidate_id},
                    {"$set": update_data, "$inc": {"interview_attempts": 1}}
                )
            else:
                result = await self.candidate_collection.update_one(
                    {"candidate_id": candidate_id},
                    {"$set": update_data}
                )
            
            if result.modified_count > 0:
                logger.info(f"Updated candidate {candidate_id} status to {status}")
                return True
            else:
                logger.warning(f"No candidate found with ID {candidate_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating candidate status: {e}")
            raise
