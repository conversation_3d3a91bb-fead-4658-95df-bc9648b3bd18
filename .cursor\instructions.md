# Interview Scheduling Microservice - Development Instructions

## Project Overview
Building a standalone Python/FastAPI microservice for automated interview scheduling using Plivo voice calls and MongoDB for data persistence.

## Key Implementation Guidelines

### 1. Database Models (Priority: HIGH)
- Implement exactly 3 collections: Candidate<PERSON>tatus, Panelist, Interview
- Use Motor (async MongoDB driver) with Pydantic models
- Follow the exact schema from PRD Section 5
- Implement proper indexes for phone_number, candidate_id, panelist_id

### 2. API Endpoints (Priority: HIGH)
Implement exactly these endpoints:
- `POST /api/v1/start-scheduling` - Initialize scheduling process
- `POST /plivo/status-callback` - Handle Plivo webhooks

### 3. Celery Tasks (Priority: HIGH)
- Create `schedule_interview_task` for processing candidates
- Implement retry logic (max 3 attempts, 30-minute delay)
- Handle panelist selection and slot booking logic

### 4. Plivo Integration (Priority: CRITICAL)
- Use Agent Flow API: `https://agentflow.plivo.com/v1/account/{PLIVO_ACCOUNT_ID}/flow/{PLIVO_INTERVIEW_FLOW_ID}`
- Send variables for dynamic content (candidate_name, available_slots_data, etc.)
- Process webhook responses for DTMF input and call status

### 5. Load Balancing Logic (Priority: MEDIUM)
- Implement round-robin panelist selection
- Handle slot availability checking
- Update slot status correctly (is_booked = true/false)

### 6. Environment Configuration
Required environment variables:
- MONGO_URI
- REDIS_HOST, REDIS_PORT
- PLIVO_ACCOUNT_ID, PLIVO_INTERVIEW_FLOW_ID
- PLIVO_WEBHOOK_URL

### 7. Status Transitions
Implement exact status flow:
- PENDING_CALL → CALL_INITIATED → (INTERVIEW_SCHEDULED | RETRY_REQUIRED | NOT_INTERESTED | MAX_RETRIES_EXCEEDED)

## Development Workflow
1. Start with database models and connection setup
2. Implement core API endpoints
3. Add Celery task processing
4. Integrate Plivo API calls
5. Handle webhook processing
6. Add error handling and logging
7. Write comprehensive tests

## Code Quality Standards
- Use type hints everywhere
- Add docstrings to all functions
- Implement proper exception handling
- Use async/await consistently
- Follow clean architecture principles
- Write unit tests for all components

## Testing Strategy
- Mock Plivo API calls in tests
- Test webhook endpoint thoroughly
- Test Celery task execution
- Test database operations
- Test error scenarios and retries