"""
Plivo service for Interview Scheduling Microservice.

Handles integration with Plivo Agent Flow API for making
automated interview scheduling calls.
"""

import logging
from typing import Dict, Any, List, Optional

import httpx
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import get_settings
from app.models import Panelist

logger = logging.getLogger(__name__)


class PlivoService:
    """
    Service for integrating with Plivo Agent Flow API.
    
    Handles call initiation and data preparation for the
    Plivo Agent Flow system.
    """
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """
        Initialize the Plivo service.
        
        Args:
            database: MongoDB database instance
        """
        self.db = database
        self.settings = get_settings()
        self.base_url = "https://agentflow.plivo.com/v1"
    
    async def initiate_interview_call(
        self,
        candidate_id: str,
        candidate_name: str,
        candidate_phone: str,
        company_name: str,
        round_info: str,
        job_id: str,
        panelist: Panelist
    ) -> Dict[str, Any]:
        """
        Initiate an interview scheduling call via Plivo Agent Flow.
        
        Args:
            candidate_id: ID of the candidate
            candidate_name: Name of the candidate
            candidate_phone: Phone number of the candidate
            company_name: Name of the company
            round_info: Interview round information
            job_id: Job identifier
            panelist: Panelist object with available slots
            
        Returns:
            Dict[str, Any]: Plivo API response
        """
        try:
            logger.info(f"Initiating Plivo call for candidate {candidate_id}")
            
            # Prepare available slots data for Agent Flow
            available_slots_data = self._prepare_slots_data(panelist)
            
            # Prepare call data
            call_data = {
                "to": candidate_phone,
                "webhook_endpoint": f"{self.settings.plivo_webhook_url}/plivo/status-callback",
                "variables": {
                    "candidate_name": candidate_name,
                    "company_name": company_name,
                    "round_info": round_info,
                    "job_id": job_id,
                    "panelist_name": panelist.name,
                    "available_slots_data": available_slots_data,
                    "candidate_id": candidate_id  # For webhook processing
                }
            }
            
            # Make API call to Plivo Agent Flow
            url = f"{self.base_url}/account/{self.settings.plivo_account_id}/flow/{self.settings.plivo_interview_flow_id}"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=call_data,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Basic {self._get_auth_header()}"
                    },
                    timeout=30.0
                )
                
                response.raise_for_status()
                result = response.json()
                
                logger.info(f"Successfully initiated Plivo call for candidate {candidate_id}")
                return {
                    "status": "success",
                    "call_uuid": result.get("call_uuid"),
                    "message": "Call initiated successfully"
                }
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error initiating Plivo call for candidate {candidate_id}: {e}")
            return {
                "status": "error",
                "error": "http_error",
                "message": f"HTTP {e.response.status_code}: {e.response.text}"
            }
        except httpx.RequestError as e:
            logger.error(f"Request error initiating Plivo call for candidate {candidate_id}: {e}")
            return {
                "status": "error",
                "error": "request_error",
                "message": str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error initiating Plivo call for candidate {candidate_id}: {e}")
            return {
                "status": "error",
                "error": "unexpected_error",
                "message": str(e)
            }
    
    def _prepare_slots_data(self, panelist: Panelist) -> List[Dict[str, Any]]:
        """
        Prepare available slots data for Plivo Agent Flow.
        
        Converts panelist slots into a format suitable for the
        Agent Flow to present to candidates.
        
        Args:
            panelist: Panelist object with available slots
            
        Returns:
            List[Dict[str, Any]]: Formatted slots data
        """
        available_slots = panelist.get_available_slots()
        slots_data = []
        
        for i, slot in enumerate(available_slots[:7]):  # Limit to 7 slots (DTMF 1-7)
            dtmf_digit = str(i + 1)
            display_text = self._format_slot_display(slot)
            
            slots_data.append({
                "dtmf_digit": dtmf_digit,
                "display_text": display_text,
                "slot_id": slot.slot_id,
                "date": slot.date.isoformat(),
                "start_time": slot.start_time,
                "end_time": slot.end_time,
                "timezone": slot.timezone
            })
        
        return slots_data
    
    def _format_slot_display(self, slot) -> str:
        """
        Format a slot for display in the voice call.
        
        Args:
            slot: Slot object
            
        Returns:
            str: Formatted display text
        """
        # Format date for voice (e.g., "August 1st")
        date_str = slot.date.strftime("%B %d")
        if date_str.endswith("1") and not date_str.endswith("11"):
            date_str += "st"
        elif date_str.endswith("2") and not date_str.endswith("12"):
            date_str += "nd"
        elif date_str.endswith("3") and not date_str.endswith("13"):
            date_str += "rd"
        else:
            date_str += "th"
        
        return f"{date_str}, {slot.start_time}"
    
    def _get_auth_header(self) -> str:
        """
        Get the authorization header for Plivo API.
        
        Returns:
            str: Base64 encoded auth header
        """
        # For MVP, we'll use a placeholder
        # In production, this would use actual Plivo credentials
        import base64
        
        if self.settings.plivo_auth_token:
            auth_string = f"{self.settings.plivo_account_id}:{self.settings.plivo_auth_token}"
            return base64.b64encode(auth_string.encode()).decode()
        else:
            # Placeholder for MVP
            return "placeholder_auth_header"
    
    async def validate_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Validate Plivo webhook signature for security.
        
        Args:
            payload: Raw webhook payload
            signature: Plivo signature header
            
        Returns:
            bool: True if signature is valid, False otherwise
        """
        try:
            # For MVP, we'll skip signature validation
            # In production, this would validate the HMAC signature
            logger.info("Webhook signature validation (placeholder for MVP)")
            return True
            
        except Exception as e:
            logger.error(f"Error validating webhook signature: {e}")
            return False
