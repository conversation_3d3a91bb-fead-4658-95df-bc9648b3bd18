"""
API v1 router for Interview Scheduling Microservice.

Contains the main API endpoints for version 1 of the service.
"""

import logging
from typing import List

from fastapi import APIRouter, HTTPException, status
from pymongo.errors import DuplicateKeyError

from app.core.database import get_database
from app.models import Candidate<PERSON>tat<PERSON>, CandidateStatusCreate, Panelist, PanelistCreate
from app.api.v1.schemas import (
    StartSchedulingRequest,
    StartSchedulingResponse,
    ErrorResponse
)
from app.services.scheduling_service import SchedulingService

logger = logging.getLogger(__name__)

api_router = APIRouter()


@api_router.post(
    "/start-scheduling",
    response_model=StartSchedulingResponse,
    status_code=status.HTTP_202_ACCEPTED,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)
async def start_scheduling(request: StartSchedulingRequest) -> StartSchedulingResponse:
    """
    Initialize the interview scheduling process.
    
    This endpoint receives candidate and panelist data, stores it in the database,
    and initiates the scheduling process by adding candidates to the Celery queue.
    
    Args:
        request: StartSchedulingRequest containing candidates, panelists, and job info
        
    Returns:
        StartSchedulingResponse: Response indicating successful initiation
        
    Raises:
        HTTPException: If there's an error processing the request
    """
    try:
        logger.info(f"Starting scheduling process for job {request.job_id}")
        
        db = get_database()
        scheduling_service = SchedulingService(db)
        
        # Process candidates
        candidates_processed = 0
        candidate_status_collection = db.candidate_status
        
        for candidate_data in request.candidates:
            try:
                # Create CandidateStatus record
                candidate_status = CandidateStatusCreate(
                    candidate_id=candidate_data.candidate_id,
                    name=candidate_data.name,
                    phone_number=candidate_data.phone_number,
                    email=candidate_data.email,
                    job_id=request.job_id,
                    round_info=request.round_info,
                    company_name=request.company_name
                )
                
                # Insert into database
                candidate_dict = candidate_status.dict()
                candidate_dict["status"] = "PENDING_CALL"
                candidate_dict["interview_attempts"] = 0
                
                await candidate_status_collection.insert_one(candidate_dict)
                candidates_processed += 1
                
                logger.info(f"Created candidate status for {candidate_data.candidate_id}")
                
            except DuplicateKeyError:
                logger.warning(f"Candidate {candidate_data.candidate_id} already exists, skipping")
                continue
            except Exception as e:
                logger.error(f"Error processing candidate {candidate_data.candidate_id}: {e}")
                continue
        
        # Process panelists
        panelists_processed = 0
        panelist_collection = db.panelist
        
        for panelist_data in request.panelists:
            try:
                # Create Panelist record
                panelist = PanelistCreate(
                    panelist_id=panelist_data.panelist_id,
                    name=panelist_data.name,
                    email=panelist_data.email,
                    available_slots=panelist_data.available_slots
                )
                
                # Insert into database
                panelist_dict = panelist.dict()
                await panelist_collection.insert_one(panelist_dict)
                panelists_processed += 1
                
                logger.info(f"Created panelist record for {panelist_data.panelist_id}")
                
            except DuplicateKeyError:
                logger.warning(f"Panelist {panelist_data.panelist_id} already exists, skipping")
                continue
            except Exception as e:
                logger.error(f"Error processing panelist {panelist_data.panelist_id}: {e}")
                continue
        
        # Initiate scheduling for all candidates
        scheduling_initiated = False
        try:
            await scheduling_service.initiate_scheduling_for_all_candidates()
            scheduling_initiated = True
            logger.info("Successfully initiated scheduling for all candidates")
        except Exception as e:
            logger.error(f"Error initiating scheduling: {e}")
            # Don't raise here as data was successfully stored
        
        response = StartSchedulingResponse(
            message="Scheduling process initiated successfully",
            candidates_processed=candidates_processed,
            panelists_processed=panelists_processed,
            scheduling_initiated=scheduling_initiated
        )
        
        logger.info(f"Scheduling initiation completed: {candidates_processed} candidates, "
                   f"{panelists_processed} panelists processed")
        
        return response
        
    except Exception as e:
        logger.error(f"Error in start_scheduling endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@api_router.get("/health")
async def health_check():
    """
    Health check endpoint for API v1.
    
    Returns:
        dict: Health status information
    """
    return {
        "status": "healthy",
        "version": "v1",
        "service": "interview-scheduling-api"
    }
