"""
Plivo webhook router for Interview Scheduling Microservice.

Handles webhooks from Plivo for call status updates and DTMF input.
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status, Request

from app.core.database import get_database
from app.api.v1.schemas import PlivoWebhookRequest, PlivoWebhookResponse, ErrorResponse
from app.services.webhook_service import WebhookService

logger = logging.getLogger(__name__)

plivo_router = APIRouter()


@plivo_router.post(
    "/status-callback",
    response_model=PlivoWebhookResponse,
    status_code=status.HTTP_200_OK,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)
async def plivo_status_callback(request: Request) -> PlivoWebhookResponse:
    """
    Handle Plivo webhook callbacks for call status and DTMF input.
    
    This endpoint receives webhooks from Plivo containing call status updates
    and DTMF input from candidates during the interview scheduling call.
    
    Args:
        request: FastAPI Request object containing webhook data
        
    Returns:
        PlivoWebhookResponse: Response indicating successful processing
        
    Raises:
        HTTPException: If there's an error processing the webhook
    """
    try:
        # Get raw request data
        raw_data = await request.json() if request.headers.get("content-type") == "application/json" else await request.form()
        
        logger.info(f"Received Plivo webhook: {raw_data}")
        
        # Convert to our webhook model for validation
        webhook_data = PlivoWebhookRequest(**dict(raw_data))
        
        # Get database and webhook service
        db = get_database()
        webhook_service = WebhookService(db)
        
        # Process the webhook
        result = await webhook_service.process_plivo_webhook(webhook_data)
        
        response = PlivoWebhookResponse(
            status="success",
            message=f"Webhook processed successfully: {result['action']}"
        )
        
        logger.info(f"Successfully processed Plivo webhook for {webhook_data.To}")
        return response
        
    except ValueError as e:
        logger.error(f"Validation error in Plivo webhook: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid webhook data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error processing Plivo webhook: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@plivo_router.get("/health")
async def plivo_health_check():
    """
    Health check endpoint for Plivo webhooks.
    
    Returns:
        dict: Health status information
    """
    return {
        "status": "healthy",
        "service": "plivo-webhook-handler"
    }
